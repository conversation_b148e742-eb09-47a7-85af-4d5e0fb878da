#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比例计算中的数组判断问题
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ratio_calculation():
    """测试比例计算中的数组判断问题"""
    print("开始测试比例计算...")
    
    try:
        # 创建测试数据，模拟实际的复权数据
        original_data = pd.DataFrame({
            'close': [10.0, 11.0, 12.0, 13.0, 14.0],
            'open': [9.5, 10.5, 11.5, 12.5, 13.5]
        }, index=['20240101000000', '20240102000000', '20240103000000', '20240104000000', '20240105000000'])
        
        # 创建复权数据，包含一些可能导致问题的值
        adjusted_data = pd.DataFrame({
            'close': [10.5, 11.5, 12.5, 13.5, 14.5],
            'open': [10.0, 11.0, 12.0, 13.0, 14.0]
        }, index=['20240101000000', '20240102000000', '20240103000000', '20240104000000', '20240105000000'])
        
        print(f"原始数据:\n{original_data}")
        print(f"复权数据:\n{adjusted_data}")
        
        # 模拟validate_adjustment_result中的比例计算
        print("开始比例计算...")
        ratio_data = adjusted_data / original_data.replace(0, 1e-10)
        print(f"比例数据:\n{ratio_data}")
        
        # 测试max().max()操作
        print("测试max().max()操作...")
        max_ratio = ratio_data.max().max() if not original_data.empty else 1
        min_ratio = ratio_data.min().min() if not original_data.empty else 1
        
        print(f"最大比例: {max_ratio}, 类型: {type(max_ratio)}")
        print(f"最小比例: {min_ratio}, 类型: {type(min_ratio)}")
        
        # 测试safe_to_float_scalar函数
        def safe_to_float_scalar(value):
            """安全转换为浮点标量值"""
            try:
                if pd.isna(value):
                    return 1.0  # NaN值默认为1
                elif hasattr(value, 'item'):
                    return float(value.item())  # numpy标量
                elif hasattr(value, '__float__'):
                    return float(value)  # 可转换为float的对象
                elif isinstance(value, (list, tuple, np.ndarray)) and len(value) == 1:
                    return float(value[0])  # 单元素数组
                else:
                    return float(value)  # 直接转换
            except (ValueError, TypeError, AttributeError):
                print(f"无法转换值为标量: {value}, 类型: {type(value)}")
                return 1.0  # 转换失败时返回默认值

        max_ratio_scalar = safe_to_float_scalar(max_ratio)
        min_ratio_scalar = safe_to_float_scalar(min_ratio)
        
        print(f"转换后最大比例: {max_ratio_scalar}, 类型: {type(max_ratio_scalar)}")
        print(f"转换后最小比例: {min_ratio_scalar}, 类型: {type(min_ratio_scalar)}")
        
        # 测试比较操作
        print("测试比较操作...")
        if max_ratio_scalar > 10 or min_ratio_scalar < 0.1:
            print(f"比例异常: 最大{max_ratio_scalar:.2f}, 最小{min_ratio_scalar:.2f}")
        else:
            print("比例正常")
        
        print("比例计算测试成功！")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_ratio_calculation()
