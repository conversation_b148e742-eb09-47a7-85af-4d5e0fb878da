#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实复权数据处理，验证修复效果
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.data_processor.adjustment.adjustment_synthesizer import AdjustmentSynthesizer
import logging

def test_real_adjustment():
    """测试真实复权数据处理"""
    # 设置简单的日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("test_real_adjustment")
    
    try:
        # 创建复权合成器
        synthesizer = AdjustmentSynthesizer()
        
        # 创建模拟的真实数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        dates_str = [d.strftime('%Y%m%d%H%M%S') for d in dates]
        
        # 模拟价格数据（使用字符串索引）
        price_data = pd.DataFrame({
            'open': np.random.uniform(10, 20, 50),
            'high': np.random.uniform(20, 30, 50),
            'low': np.random.uniform(5, 15, 50),
            'close': np.random.uniform(10, 25, 50),
            'volume': np.random.uniform(1000, 5000, 50)
        }, index=dates_str)
        
        logger.info("创建模拟价格数据完成")
        logger.info(f"价格数据形状: {price_data.shape}")
        logger.info(f"价格数据索引类型: {type(price_data.index[0])}")
        
        # 创建模拟的复权因子数据
        dividend_dates = ['20240115', '20240215', '20240315']
        dividend_factors = pd.DataFrame({
            'time': dividend_dates,
            'dr': [0.95, 0.98, 0.96]  # 复权比例
        })
        
        logger.info("创建模拟复权因子数据完成")
        logger.info(f"复权因子数据: {dividend_factors}")
        
        # 测试前复权计算
        logger.info("开始测试前复权计算...")
        
        # 直接调用内部方法进行测试
        adjusted_data = synthesizer._calculate_forward_adjustment(
            price_data, dividend_factors, "ratio"
        )
        
        if adjusted_data is not None:
            logger.info("✅ 前复权计算成功完成")
            logger.info(f"复权后数据形状: {adjusted_data.shape}")
            logger.info(f"复权后数据索引类型: {type(adjusted_data.index[0])}")
            
            # 显示前几行数据
            logger.info("复权前数据（前5行）:")
            logger.info(f"{price_data.head()}")
            
            logger.info("复权后数据（前5行）:")
            logger.info(f"{adjusted_data.head()}")
            
            return True
        else:
            logger.error("❌ 前复权计算失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("开始测试真实复权数据处理...")
    success = test_real_adjustment()
    
    if success:
        print("✅ 真实复权数据处理测试成功")
    else:
        print("❌ 真实复权数据处理测试失败")
