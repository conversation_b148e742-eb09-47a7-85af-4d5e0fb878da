#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟实际复权处理流程的测试
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_real_adjustment_flow():
    """测试实际复权处理流程"""
    print("开始测试实际复权处理流程...")
    
    try:
        from utils.data_processor.adjustment.forward_adjustment_engine import forward_adjustment_engine
        
        # 创建模拟实际tick数据的DataFrame，包含多列
        original_data = pd.DataFrame({
            'time': [1701637710245, 1701637713158, 1701637716071, 1701637718983, 1701637721896],
            'lastPrice': [13.55, 13.55, 13.55, 13.55, 13.55],
            'open': [13.74, 13.74, 13.74, 13.74, 13.74],
            'high': [13.79, 13.79, 13.79, 13.79, 13.79],
            'low': [13.43, 13.43, 13.43, 13.43, 13.43],
            'lastClose': [13.70, 13.70, 13.70, 13.70, 13.70],
            'amount': [730031.35, 730031.35, 730031.35, 730031.35, 730031.35],
            'volume': [730031.35, 730031.35, 730031.35, 730031.35, 730031.35]
        }, index=['20250715145702', '20250715145705', '20250715145708', '20250715145711', '20250715145714'])
        
        print(f"原始数据:\n{original_data}")
        
        # 模拟复权处理后的数据，包含一些可能的问题值
        adjusted_data = original_data.copy()
        
        # 应用复权比例（模拟实际复权计算）
        adjustment_ratio = 1.0123  # 模拟复权比例
        price_columns = ['lastPrice', 'open', 'high', 'low', 'lastClose']
        
        for col in price_columns:
            if col in adjusted_data.columns:
                adjusted_data[col] = adjusted_data[col] * adjustment_ratio
        
        # 添加一些边界情况
        adjusted_data.loc['20250715145708', 'lastPrice'] = 0.0  # 零值
        adjusted_data.loc['20250715145711', 'open'] = -0.1  # 负值
        
        print(f"复权数据:\n{adjusted_data}")
        
        # 调用验证函数
        print("调用validate_adjustment_result函数...")
        result = forward_adjustment_engine.validate_adjustment_result(original_data, adjusted_data)
        
        print("验证成功！")
        print(f"验证结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_with_complex_data():
    """测试复杂数据情况"""
    print("开始测试复杂数据情况...")
    
    try:
        from utils.data_processor.adjustment.forward_adjustment_engine import forward_adjustment_engine
        
        # 创建更复杂的数据，包含NaN、inf等特殊值
        np.random.seed(42)
        size = 100
        
        original_data = pd.DataFrame({
            'lastPrice': np.random.uniform(10, 20, size),
            'open': np.random.uniform(10, 20, size),
            'high': np.random.uniform(15, 25, size),
            'low': np.random.uniform(5, 15, size),
            'close': np.random.uniform(10, 20, size),
            'volume': np.random.uniform(1000, 10000, size)
        }, index=[f"2024010{i//10:01d}{i%10:02d}0000" for i in range(size)])
        
        # 创建复权数据，包含各种边界情况
        adjusted_data = original_data.copy()
        
        # 应用不同的复权比例
        for i, col in enumerate(['lastPrice', 'open', 'high', 'low', 'close']):
            ratio = 1.0 + (i * 0.01)  # 不同列使用不同比例
            adjusted_data[col] = adjusted_data[col] * ratio
        
        # 添加一些特殊值
        adjusted_data.iloc[10:15, 0] = -1  # 负值
        adjusted_data.iloc[20:22, 1] = 0   # 零值
        adjusted_data.iloc[30, 2] = np.inf  # 无穷大
        adjusted_data.iloc[31, 3] = np.nan  # NaN值
        
        print(f"原始数据形状: {original_data.shape}")
        print(f"复权数据形状: {adjusted_data.shape}")
        print(f"包含特殊值的数量:")
        print(f"  负值: {(adjusted_data < 0).sum().sum()}")
        print(f"  零值: {(adjusted_data == 0).sum().sum()}")
        print(f"  无穷大: {np.isinf(adjusted_data).sum().sum()}")
        print(f"  NaN值: {adjusted_data.isna().sum().sum()}")
        
        # 调用验证函数
        print("调用validate_adjustment_result函数...")
        result = forward_adjustment_engine.validate_adjustment_result(original_data, adjusted_data)
        
        print("验证成功！")
        print(f"验证结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("开始实际复权处理流程测试")
    print("=" * 60)
    
    # 测试1：模拟实际流程
    test1_result = test_real_adjustment_flow()
    
    print("\n" + "=" * 60)
    
    # 测试2：复杂数据情况
    test2_result = test_with_complex_data()
    
    print("\n" + "=" * 60)
    if test1_result and test2_result:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败")
    print("=" * 60)
