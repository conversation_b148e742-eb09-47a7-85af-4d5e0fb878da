#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的验证函数测试
直接测试validate_adjustment_result函数
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_simple_validation():
    """简单测试验证函数"""
    print("开始简单验证测试...")
    
    try:
        # 直接导入函数
        from utils.data_processor.adjustment.forward_adjustment_engine import forward_adjustment_engine
        
        # 创建简单测试数据
        original_data = pd.DataFrame({
            'close': [10.0, 11.0, 12.0, 13.0, 14.0]
        }, index=['20240101000000', '20240102000000', '20240103000000', '20240104000000', '20240105000000'])
        
        # 创建包含负值的复权数据
        adjusted_data = pd.DataFrame({
            'close': [10.5, -1.0, 12.5, 13.5, 14.5]  # 包含一个负值
        }, index=['20240101000000', '20240102000000', '20240103000000', '20240104000000', '20240105000000'])
        
        print(f"原始数据:\n{original_data}")
        print(f"复权数据:\n{adjusted_data}")
        
        # 直接调用验证函数
        print("调用validate_adjustment_result函数...")
        result = forward_adjustment_engine.validate_adjustment_result(original_data, adjusted_data)
        
        print("验证成功！")
        print(f"验证结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_simple_validation()
