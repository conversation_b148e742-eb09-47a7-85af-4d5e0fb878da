#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数组判断错误修复效果
验证validate_adjustment_result函数是否正确处理数组判断
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_processor.adjustment.forward_adjustment_engine import forward_adjustment_engine
from utils.logger.manager import get_unified_logger
logger = get_unified_logger('test_array_judgment_fix')

def test_array_judgment_fix():
    """测试数组判断错误修复"""
    logger.info("开始测试数组判断错误修复...")
    
    try:
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        original_data = pd.DataFrame({
            'open': np.random.uniform(10, 20, 100),
            'high': np.random.uniform(20, 30, 100),
            'low': np.random.uniform(5, 15, 100),
            'close': np.random.uniform(10, 25, 100),
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates.strftime('%Y%m%d%H%M%S'))
        
        # 创建包含负值的复权数据（故意制造问题数据）
        adjusted_data = original_data.copy()
        adjusted_data.iloc[10:15, 0] = -1  # 添加一些负值
        adjusted_data.iloc[20:22, 1] = -2  # 添加更多负值
        
        logger.info(f"测试数据创建完成，原始数据形状: {original_data.shape}")
        logger.info(f"复权数据形状: {adjusted_data.shape}")
        logger.info(f"复权数据中负值数量: {(adjusted_data < 0).sum().sum()}")
        
        # 调用验证函数 - 这里应该不会出现数组判断错误
        logger.info("开始调用validate_adjustment_result函数...")
        validation_result = forward_adjustment_engine.validate_adjustment_result(
            original_data, adjusted_data
        )
        
        logger.info("验证函数调用成功！")
        logger.info(f"验证结果: {validation_result}")
        
        # 检查结果
        assert 'is_valid' in validation_result
        assert 'errors' in validation_result
        assert 'warnings' in validation_result
        assert 'statistics' in validation_result
        
        # 检查负值统计
        if 'negative_count' in validation_result['statistics']:
            negative_count = validation_result['statistics']['negative_count']
            logger.info(f"统计中的负值数量: {negative_count}")
            assert isinstance(negative_count, int), f"negative_count应该是int类型，实际是: {type(negative_count)}"
            assert negative_count > 0, "应该检测到负值"
        
        # 检查警告信息
        warnings = validation_result['warnings']
        logger.info(f"警告信息: {warnings}")
        
        # 应该有关于负值的警告
        negative_warnings = [w for w in warnings if '负值' in w]
        assert len(negative_warnings) > 0, "应该有关于负值的警告"
        
        logger.info("✅ 数组判断错误修复测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_edge_cases():
    """测试边界情况"""
    logger.info("开始测试边界情况...")
    
    try:
        # 测试空数据
        empty_df = pd.DataFrame()
        result = forward_adjustment_engine.validate_adjustment_result(empty_df, empty_df)
        logger.info(f"空数据测试结果: {result}")
        
        # 测试全零数据
        zero_data = pd.DataFrame({
            'close': [0, 0, 0, 0, 0]
        }, index=['20240101000000', '20240102000000', '20240103000000', '20240104000000', '20240105000000'])
        
        result = forward_adjustment_engine.validate_adjustment_result(zero_data, zero_data)
        logger.info(f"全零数据测试结果: {result}")
        
        # 测试全负值数据
        negative_data = pd.DataFrame({
            'close': [-1, -2, -3, -4, -5]
        }, index=['20240101000000', '20240102000000', '20240103000000', '20240104000000', '20240105000000'])
        
        result = forward_adjustment_engine.validate_adjustment_result(zero_data, negative_data)
        logger.info(f"全负值数据测试结果: {result}")
        
        logger.info("✅ 边界情况测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 边界情况测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("开始数组判断错误修复验证测试")
    logger.info("=" * 60)
    
    # 运行主要测试
    test1_result = test_array_judgment_fix()
    
    # 运行边界情况测试
    test2_result = test_edge_cases()
    
    # 总结
    logger.info("=" * 60)
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！数组判断错误修复成功！")
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
    logger.info("=" * 60)
