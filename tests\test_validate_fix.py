#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试validate_adjustment_result函数修复效果
验证数组判断错误是否已解决
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.data_processor.adjustment.forward_adjustment_engine import ForwardAdjustmentEngine
import logging

def test_validate_function():
    """测试validate_adjustment_result函数"""
    # 设置简单的日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("test_validate")
    
    try:
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=10, freq='D')
        
        # 原始数据
        original_data = pd.DataFrame({
            'open': np.random.uniform(10, 20, 10),
            'high': np.random.uniform(20, 30, 10),
            'low': np.random.uniform(5, 15, 10),
            'close': np.random.uniform(10, 25, 10),
            'volume': np.random.uniform(1000, 5000, 10)
        }, index=dates)
        
        # 复权后数据（稍微调整价格）
        adjusted_data = original_data.copy()
        adjusted_data[['open', 'high', 'low', 'close']] *= 1.1
        
        # 添加一些负值来测试负值检查
        adjusted_data.iloc[2, 0] = -1.0  # 添加一个负值
        
        logger.info("创建测试数据完成")
        logger.info(f"原始数据形状: {original_data.shape}")
        logger.info(f"复权数据形状: {adjusted_data.shape}")
        
        # 创建引擎实例
        engine = ForwardAdjustmentEngine()
        
        # 测试验证函数
        logger.info("开始测试validate_adjustment_result函数...")
        result = engine.validate_adjustment_result(original_data, adjusted_data)
        
        logger.info("验证函数测试完成")
        logger.info(f"验证结果: {result}")
        
        # 检查结果
        if result['is_valid']:
            logger.info("✅ 验证通过，函数正常工作")
        else:
            logger.warning(f"⚠️ 验证失败: {result['errors']}")
        
        if result['warnings']:
            logger.info(f"📋 警告信息: {result['warnings']}")
        
        if 'statistics' in result:
            logger.info(f"📊 统计信息: {result['statistics']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试validate_adjustment_result函数修复效果...")
    success = test_validate_function()
    
    if success:
        print("✅ 测试成功完成")
    else:
        print("❌ 测试失败")
